{"name": "copilot-api", "version": "0.5.1", "description": "Turn GitHub Copilot into OpenAI/Anthropic API compatible server. Usable with Claude Code!", "keywords": ["proxy", "github-copilot", "openai-compatible"], "homepage": "https://github.com/ericc-ch/copilot-api", "bugs": "https://github.com/ericc-ch/copilot-api/issues", "repository": {"type": "git", "url": "git+https://github.com/ericc-ch/copilot-api.git"}, "author": "<PERSON><PERSON> <<EMAIL>>", "type": "module", "bin": {"copilot-api": "./dist/main.js"}, "files": ["dist"], "scripts": {"build": "tsup", "dev": "bun run --watch ./src/main.ts", "knip": "knip-bun", "lint": "eslint .", "prepack": "bun run build", "prepare": "simple-git-hooks", "release": "bumpp && bun publish --access public", "start": "NODE_ENV=production bun run ./src/main.ts"}, "simple-git-hooks": {"pre-commit": "bunx lint-staged"}, "lint-staged": {"*": "bunx eslint --fix"}, "dependencies": {"citty": "^0.1.6", "clipboardy": "^4.0.0", "consola": "^3.4.2", "fetch-event-stream": "^0.1.5", "gpt-tokenizer": "^3.0.1", "hono": "^4.8.1", "srvx": "^0.8.0", "tiny-invariant": "^1.3.3"}, "devDependencies": {"@echristian/eslint-config": "^0.0.43", "@types/bun": "^1.2.16", "bumpp": "^10.2.0", "eslint": "^9.29.0", "knip": "^5.61.2", "lint-staged": "^16.1.2", "prettier-plugin-packagejson": "^2.5.15", "simple-git-hooks": "^2.13.0", "tsup": "^8.5.0", "typescript": "^5.8.3"}}